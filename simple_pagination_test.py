#!/usr/bin/env python3
"""
Απλό test για pagination - χρησιμοποιεί ήδη ανοιχτή σελίδα
"""

import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.action_chains import ActionChains

def test_pagination_on_current_page():
    """
    Δοκιμάζει pagination στην τρέχουσα σελίδα
    """
    print("🚀 Ξεκινά απλό test pagination...")
    
    # Συνδέεται σε ήδη ανοιχτό Chrome instance
    options = Options()
    options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
    
    try:
        driver = webdriver.Chrome(options=options)
        print("✅ Συνδέθηκε στο ανοιχτό Chrome")
        
        current_url = driver.current_url
        print(f"📍 Τρέχον URL: {current_url}")
        
        # Ελέγχει αν είμαστε στη σωστή σελίδα
        if "jobads/apps/" not in current_url:
            print("❌ Δεν είμαστε στη σελίδα υποψηφίων. Πλοηγηθείτε εκεί πρώτα.")
            return False
        
        # Αναλύει το pagination DOM
        print("\n🔍 Αναλύει το pagination DOM...")
        
        # Βρίσκει pagination elements
        pagination_elements = driver.find_elements(By.XPATH, "//div[contains(@class,'pager')] | //div[contains(@class,'pagination')]")
        print(f"📊 Βρέθηκαν {len(pagination_elements)} pagination elements")
        
        for i, elem in enumerate(pagination_elements):
            print(f"  Element {i+1}:")
            print(f"    Class: {elem.get_attribute('class')}")
            print(f"    ID: {elem.get_attribute('id')}")
            print(f"    HTML: {elem.get_attribute('outerHTML')[:150]}...")
        
        # Βρίσκει κουμπιά σελίδων
        page_buttons = driver.find_elements(By.XPATH, "//a[contains(@class,'e-numericitem')] | //button[contains(@class,'e-numericitem')]")
        print(f"\n🔘 Βρέθηκαν {len(page_buttons)} κουμπιά σελίδων")
        
        for i, btn in enumerate(page_buttons):
            print(f"  Κουμπί {i+1}:")
            print(f"    Text: '{btn.text}'")
            print(f"    Class: {btn.get_attribute('class')}")
            print(f"    Aria-label: {btn.get_attribute('aria-label')}")
            print(f"    Tabindex: {btn.get_attribute('tabindex')}")
        
        # Δοκιμάζει να πάει στη σελίδα 2
        print("\n🎯 Δοκιμάζει να πάει στη σελίδα 2...")
        
        # Μέθοδος 1: Syncfusion API
        print("🔧 Μέθοδος 1: Syncfusion API...")
        result = driver.execute_script("""
            var pagerElements = document.querySelectorAll('.e-pager');
            console.log('Pager elements found:', pagerElements.length);
            
            if (pagerElements.length > 0) {
                var pager = pagerElements[0];
                console.log('Pager:', pager);
                console.log('Pager instances:', pager.ej2_instances);
                
                if (pager.ej2_instances && pager.ej2_instances.length > 0) {
                    var pagerInstance = pager.ej2_instances[0];
                    console.log('Pager instance:', pagerInstance);
                    console.log('Current page:', pagerInstance.currentPage);
                    console.log('Total pages:', pagerInstance.totalPages);
                    
                    if (pagerInstance.goToPage) {
                        console.log('Calling goToPage(2)');
                        pagerInstance.goToPage(2);
                        return 'goToPage called';
                    }
                }
            }
            return 'No pager instance found';
        """)
        print(f"  📊 Αποτέλεσμα: {result}")
        
        time.sleep(3)
        
        # Ελέγχει αν άλλαξε σελίδα
        new_url = driver.current_url
        if new_url != current_url:
            print(f"✅ Επιτυχία! Νέο URL: {new_url}")
            return True
        
        # Μέθοδος 2: Κλικ στο κουμπί "2"
        print("\n🔧 Μέθοδος 2: Κλικ στο κουμπί '2'...")
        try:
            page2_button = driver.find_element(By.XPATH, "//a[contains(@class,'e-numericitem') and text()='2']")
            print(f"  Βρέθηκε κουμπί: {page2_button.get_attribute('outerHTML')[:100]}...")
            
            # Scroll στο κουμπί
            driver.execute_script("arguments[0].scrollIntoView(true);", page2_button)
            time.sleep(1)
            
            # Κλικ με ActionChains
            actions = ActionChains(driver)
            actions.move_to_element(page2_button).click().perform()
            
            time.sleep(3)
            
            new_url = driver.current_url
            if new_url != current_url:
                print(f"✅ Επιτυχία με ActionChains! Νέο URL: {new_url}")
                return True
            
        except Exception as e:
            print(f"  ❌ Κλικ απέτυχε: {e}")
        
        # Μέθοδος 3: JavaScript events
        print("\n🔧 Μέθοδος 3: JavaScript events...")
        result = driver.execute_script("""
            var buttons = document.querySelectorAll('.e-numericitem');
            for (var i = 0; i < buttons.length; i++) {
                if (buttons[i].textContent.trim() === '2') {
                    var btn = buttons[i];
                    console.log('Found button 2:', btn);
                    
                    // Προσπαθεί διάφορα events
                    btn.focus();
                    btn.dispatchEvent(new Event('focus', {bubbles: true}));
                    btn.dispatchEvent(new Event('click', {bubbles: true}));
                    btn.dispatchEvent(new MouseEvent('click', {
                        view: window,
                        bubbles: true,
                        cancelable: true
                    }));
                    
                    return 'Events dispatched to button 2';
                }
            }
            return 'Button 2 not found';
        """)
        print(f"  📊 Αποτέλεσμα: {result}")
        
        time.sleep(3)
        
        new_url = driver.current_url
        if new_url != current_url:
            print(f"✅ Επιτυχία με JavaScript events! Νέο URL: {new_url}")
            return True
        
        print("❌ Όλες οι μέθοδοι απέτυχαν")
        return False
        
    except Exception as e:
        print(f"❌ Σφάλμα: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 ΑΠΛΟ TEST PAGINATION")
    print("=" * 60)
    print("📋 Οδηγίες:")
    print("1. Ανοίξτε Chrome με --remote-debugging-port=9222")
    print("2. Πλοηγηθείτε στη σελίδα υποψηφίων")
    print("3. Τρέξτε αυτό το script")
    print("=" * 60)
    
    success = test_pagination_on_current_page()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ΕΠΙΤΥΧΙΑ! Το pagination λειτουργεί!")
    else:
        print("❌ ΑΠΟΤΥΧΙΑ! Το pagination δεν λειτουργεί.")
    print("=" * 60)
    
    input("\nΠατήστε Enter για έξοδο...")
