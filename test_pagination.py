#!/usr/bin/env python3
"""
Test script για να ελέγξει αν το pagination λειτουργεί σωστά
πριν τρέξουμε το πλήρες κατέβασμα βιογραφικών.
"""

import time
import os
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

# Φορτώνει τις μεταβλητές από το .env αρχείο
load_dotenv()

# Στοιχεία σύνδεσης από .env
JOBFIND_EMAIL = os.getenv("JOBFIND_EMAIL")
JOBFIND_PASS = os.getenv("JOBFIND_PASS")

print(f"Χρησιμοποιεί email: {JOBFIND_EMAIL}")
print(f"Χρησιμοποιεί κωδικό: {JOBFIND_PASS}")

# Ρύθμιση Chrome
chrome_options = Options()
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)

driver = webdriver.Chrome(options=chrome_options)
driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
wait = WebDriverWait(driver, 20)

def login():
    print("Ανοίγει η σελίδα login...")
    driver.get("https://business.jobfind.gr/signin")

    # Περιμένει να φορτώσει η σελίδα και να εμφανιστούν τα πεδία
    print("Περιμένει για τα πεδία login...")

    # Προσπαθεί να βρει το email field με διάφορους τρόπους
    try:
        email_field = wait.until(EC.presence_of_element_located((By.XPATH, "//input[contains(@placeholder, 'Email') or @type='email' or contains(@name, 'email') or contains(@id, 'email')]")))
    except:
        # Εναλλακτικά, βρες το πρώτο input field
        email_field = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@type='text' or @type='email']")))

    email_field.send_keys(JOBFIND_EMAIL)
    print(f"Εισήχθη email: {JOBFIND_EMAIL}")

    # Προσπαθεί να βρει το password field
    try:
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
    except:
        password_field = driver.find_element(By.XPATH, "//input[contains(@placeholder, 'password') or contains(@placeholder, 'κωδικός') or contains(@name, 'password')]")

    password_field.send_keys(JOBFIND_PASS)
    print("Εισήχθη κωδικός πρόσβασης")

    # Βρίσκει και πατάει το κουμπί login
    try:
        login_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Είσοδος')] | //input[@type='submit' and contains(@value, 'Είσοδος')]")
        login_button.click()
    except:
        # Εναλλακτικά, πάτα Enter στο password field
        password_field.send_keys(Keys.RETURN)

    print("Πατήθηκε το κουμπί login...")

    # Περιμένει να ολοκληρωθεί το login
    time.sleep(3)
    print("Login ολοκληρώθηκε.")

def goto_my_ads():
    print("Αναζητά το link για τις αγγελίες...")

    # Προσπαθεί να βρει το κουμπί "Δείτε τις αγγελίες σας"
    try:
        ads_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Δείτε τις αγγελίες σας')] | //button[contains(text(), 'Δείτε τις αγγελίες σας')]")))
        driver.execute_script("arguments[0].click();", ads_button)
        print("Πατήθηκε το κουμπί 'Δείτε τις αγγελίες σας'")
        return
    except Exception as e:
        print(f"Δεν βρέθηκε το κουμπί 'Δείτε τις αγγελίες σας': {e}")

    # Εναλλακτικά, προσπαθεί να βρει άλλα links
    try:
        # Προσπάθεια για "Οι Αγγελίες μου"
        ads_link = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Οι Αγγελίες μου')] | //a[contains(text(), 'αγγελίες')] | //a[contains(text(), 'Αγγελίες')]")))
        driver.execute_script("arguments[0].click();", ads_link)
        print("Πατήθηκε το link για τις αγγελίες")
    except Exception as e:
        print(f"Σφάλμα κατά την αναζήτηση αγγελιών: {e}")
        raise

def open_applications_for_ad_by_index(row_index=1):
    print(f"Αναζητά αγγελίες με κατάσταση 'Δημοσιευμένη'...")
    
    # Περιμένει να φορτώσει ο πίνακας
    time.sleep(3)
    
    try:
        # Βρίσκει όλες τις γραμμές του πίνακα
        rows = driver.find_elements(By.XPATH, "//table//tr | //tbody//tr")
        print(f"Βρέθηκαν {len(rows)} γραμμές στον πίνακα")
        
        # Ψάχνει για δημοσιευμένη αγγελία
        for i, row in enumerate(rows, 1):
            try:
                # Ελέγχει αν η γραμμή περιέχει "Δημοσιευμένη"
                if "Δημοσιευμένη" in row.text or "δημοσιευμένη" in row.text:
                    print(f"Βρέθηκε δημοσιευμένη αγγελία στη γραμμή {i}")
                    
                    # Αναζητά link για βιογραφικά
                    cv_links = row.find_elements(By.XPATH, ".//a[contains(@href, 'apps') or contains(text(), 'CV') or contains(text(), 'βιογραφικ')]")
                    
                    for link in cv_links:
                        link_text = link.text.strip()
                        if link_text.isdigit() and int(link_text) > 0:
                            cv_count = int(link_text)
                            cv_url = link.get_attribute('href')
                            print(f"  Βρέθηκε CV link: '{link_text}' -> {cv_url}")
                            print(f"Βρέθηκε δημοσιευμένη αγγελία στη γραμμή {i} με {cv_count} βιογραφικά")
                            
                            # Κλικάρει στο link
                            print(f"Πατάει στη στήλη βιογραφικά με {cv_count} βιογραφικά...")
                            driver.execute_script("arguments[0].click();", link)
                            
                            # Περιμένει να φορτώσει η λίστα υποψηφίων
                            time.sleep(5)
                            print("Φόρτωσε η λίστα υποψηφίων.")
                            return
                            
            except Exception as e:
                continue
                
        print("Δεν βρέθηκε δημοσιευμένη αγγελία με βιογραφικά")
        
    except Exception as e:
        print(f"Σφάλμα κατά την αναζήτηση αγγελιών: {e}")
        raise

def test_pagination():
    """
    Δοκιμάζει αν μπορεί να αλλάξει σελίδες στη λίστα υποψηφίων
    """
    print("\n=== ΔΟΚΙΜΗ PAGINATION ===")
    
    # Αποθηκεύει το URL της λίστας υποψηφίων
    candidates_list_url = driver.current_url
    print(f"URL λίστας υποψηφίων: {candidates_list_url}")
    
    # Ελέγχει την τρέχουσα σελίδα
    page = 1
    max_pages_to_test = 10  # Δοκιμάζει μέχρι 10 σελίδες (θα σταματήσει όταν δεν βρει άλλες)
    
    while page <= max_pages_to_test:
        print(f"\n{'='*60}")
        print(f"📄 ΔΟΚΙΜΗ ΣΕΛΙΔΑΣ {page}")
        print(f"{'='*60}")

        # Ελέγχει αν υπάρχουν πληροφορίες pagination
        try:
            pagination_info = driver.find_element(By.XPATH, "//span[contains(text(), 'από') and contains(text(), 'σελίδες') and contains(text(), 'εγγραφές')]")
            pagination_text = pagination_info.text
            print(f"📊 Pagination info: {pagination_text}")
        except:
            print("⚠️  Δεν βρέθηκαν πληροφορίες pagination")

        # Βρίσκει τους υποψηφίους στην τρέχουσα σελίδα
        try:
            candidates = driver.find_elements(By.XPATH, "//table//a[contains(@href, '/candidate/') or contains(@href, '/profile/')] | //a[contains(@href, 'candidate')] | //td//a[not(contains(@href, 'mailto'))]")
            print(f"👥 Βρέθηκαν {len(candidates)} υποψήφιοι στη σελίδα {page}")

            # Δείχνει τους πρώτους 3 υποψηφίους
            if candidates:
                print(f"📋 Πρώτοι υποψήφιοι:")
                for i, candidate in enumerate(candidates[:3]):
                    name = candidate.text.strip()
                    if name:
                        print(f"  {i+1}. {name}")
            else:
                print("❌ Δεν βρέθηκαν υποψήφιοι!")

        except Exception as e:
            print(f"❌ Σφάλμα κατά την ανάγνωση υποψηφίων: {e}")
        
        # Προσπαθεί να βρει επόμενη σελίδα
        if page < max_pages_to_test:
            print(f"\n🔍 Αναζητά επόμενη σελίδα ({page + 1})...")

            # Αποθηκεύει το τρέχον URL για σύγκριση
            current_url = driver.current_url
            print(f"📍 Τρέχον URL: {current_url}")

            # === ΜΕΘΟΔΟΣ 1: ΑΝΑΛΥΣΗ DOM ΚΑΙ JAVASCRIPT FRAMEWORK ===
            next_page_num = page + 1
            print(f"� Αναλύει το DOM για pagination framework...")

            # Εξετάζει το pagination DOM
            try:
                # Βρίσκει όλα τα pagination elements
                pagination_elements = driver.find_elements(By.XPATH, "//div[contains(@class,'pager')] | //div[contains(@class,'pagination')] | //nav")

                for i, elem in enumerate(pagination_elements):
                    print(f"  📋 Pagination Element {i+1}:")
                    print(f"    Class: {elem.get_attribute('class')}")
                    print(f"    ID: {elem.get_attribute('id')}")
                    print(f"    HTML: {elem.get_attribute('outerHTML')[:200]}...")

                # Αναζητά JavaScript events ή framework-specific attributes
                page_buttons = driver.find_elements(By.XPATH, f"//a[text()='{next_page_num}'] | //button[text()='{next_page_num}'] | //span[text()='{next_page_num}']")

                for i, btn in enumerate(page_buttons):
                    print(f"  🔘 Page Button {i+1} (σελίδα {next_page_num}):")
                    print(f"    Tag: {btn.tag_name}")
                    print(f"    Class: {btn.get_attribute('class')}")
                    print(f"    ID: {btn.get_attribute('id')}")
                    print(f"    OnClick: {btn.get_attribute('onclick')}")
                    print(f"    Data attributes: {[attr for attr in btn.get_attribute('outerHTML').split() if 'data-' in attr]}")

            except Exception as e:
                print(f"  ❌ Σφάλμα κατά την ανάλυση DOM: {e}")

            # === ΜΕΘΟΔΟΣ 2: JAVASCRIPT FRAMEWORK TRIGGERS ===
            print(f"🔧 Δοκιμάζει JavaScript framework triggers...")

            framework_success = False

            # Δοκιμάζει Syncfusion pagination με τις συγκεκριμένες κλάσεις
            try:
                print("  🔧 Δοκιμάζει Syncfusion pagination με e-pager κλάσεις...")
                result = driver.execute_script(f"""
                    // Αναζητά Syncfusion pager με τις συγκεκριμένες κλάσεις
                    var pagerElements = document.querySelectorAll('.e-pager');
                    console.log('Found pager elements:', pagerElements.length);

                    if (pagerElements.length > 0) {{
                        var pager = pagerElements[0];
                        console.log('Pager element:', pager);
                        console.log('Pager instances:', pager.ej2_instances);

                        // Προσπαθεί να βρει το pager object
                        if (pager.ej2_instances && pager.ej2_instances.length > 0) {{
                            var pagerInstance = pager.ej2_instances[0];
                            console.log('Pager instance:', pagerInstance);
                            console.log('Available methods:', Object.getOwnPropertyNames(pagerInstance));

                            if (pagerInstance.goToPage) {{
                                console.log('Calling goToPage with:', {next_page_num});
                                pagerInstance.goToPage({next_page_num});
                                return 'Syncfusion goToPage called successfully';
                            }} else if (pagerInstance.currentPage !== undefined) {{
                                console.log('Setting currentPage to:', {next_page_num});
                                pagerInstance.currentPage = {next_page_num};
                                if (pagerInstance.refresh) {{
                                    pagerInstance.refresh();
                                }}
                                return 'Syncfusion currentPage set';
                            }}
                        }}

                        // Fallback: Αναζητά κουμπί με aria-label
                        var pageButton = pager.querySelector('a[aria-label="Go to page {next_page_num}"]');
                        if (pageButton) {{
                            console.log('Found page button with aria-label:', pageButton);
                            pageButton.click();
                            return 'Syncfusion aria-label click';
                        }}

                        // Fallback: Αναζητά κουμπί με tabindex
                        var numericButtons = pager.querySelectorAll('.e-numericitem');
                        for (var i = 0; i < numericButtons.length; i++) {{
                            if (numericButtons[i].textContent.trim() === '{next_page_num}') {{
                                console.log('Found numeric button:', numericButtons[i]);
                                numericButtons[i].click();
                                return 'Syncfusion numeric button click';
                            }}
                        }}
                    }}
                    return 'Syncfusion pager not found or no suitable method';
                """)
                print(f"    📊 Syncfusion result: {result}")

                if any(keyword in result for keyword in ["successfully", "currentPage set", "aria-label click", "numeric button click"]):
                    time.sleep(4)
                    new_candidates = driver.find_elements(By.XPATH, "//table//a[contains(@href, '/candidate/') or contains(@href, '/profile/')] | //a[contains(@href, 'candidate')] | //td//a[not(contains(@href, 'mailto'))]")
                    if len(new_candidates) > 0:
                        framework_success = True
                        print("  ✅ Syncfusion pagination επιτυχής!")

            except Exception as e:
                print(f"  ❌ Syncfusion pagination απέτυχε: {e}")

            # Δοκιμάζει συγκεκριμένα Syncfusion events
            if not framework_success:
                try:
                    print("  🔧 Δοκιμάζει συγκεκριμένα Syncfusion events...")
                    result = driver.execute_script(f"""
                        // Αναζητά κουμπιά με τις συγκεκριμένες κλάσεις Syncfusion
                        var pageButtons = document.querySelectorAll('.e-numericitem');
                        console.log('Found e-numericitem buttons:', pageButtons.length);

                        for (var i = 0; i < pageButtons.length; i++) {{
                            var btn = pageButtons[i];
                            console.log('Button', i, ':', btn.textContent.trim(), btn.className);

                            if (btn.textContent.trim() === '{next_page_num}') {{
                                console.log('Found target button for page {next_page_num}');

                                // Προσπαθεί διάφορα Syncfusion-specific events
                                var events = ['click', 'mousedown', 'mouseup', 'focus', 'keydown'];
                                for (var j = 0; j < events.length; j++) {{
                                    var eventType = events[j];
                                    var event = new Event(eventType, {{
                                        bubbles: true,
                                        cancelable: true,
                                        view: window
                                    }});

                                    if (eventType === 'keydown') {{
                                        event.keyCode = 13; // Enter key
                                        event.which = 13;
                                    }}

                                    console.log('Dispatching', eventType, 'event');
                                    btn.dispatchEvent(event);
                                }}

                                // Προσπαθεί και MouseEvent
                                var mouseEvent = new MouseEvent('click', {{
                                    view: window,
                                    bubbles: true,
                                    cancelable: true,
                                    clientX: btn.getBoundingClientRect().left + 10,
                                    clientY: btn.getBoundingClientRect().top + 10
                                }});
                                btn.dispatchEvent(mouseEvent);

                                return 'Syncfusion events triggered for page {next_page_num}';
                            }}
                        }}

                        // Fallback: Αναζητά οποιοδήποτε link με το νούμερο
                        var allLinks = document.querySelectorAll('a');
                        for (var i = 0; i < allLinks.length; i++) {{
                            if (allLinks[i].textContent.trim() === '{next_page_num}') {{
                                console.log('Found fallback link:', allLinks[i]);
                                allLinks[i].click();
                                return 'Fallback link clicked';
                            }}
                        }}

                        return 'No suitable buttons found';
                    """)
                    print(f"    📊 Syncfusion events result: {result}")

                    if any(keyword in result for keyword in ["events triggered", "link clicked"]):
                        time.sleep(4)
                        new_candidates = driver.find_elements(By.XPATH, "//table//a[contains(@href, '/candidate/') or contains(@href, '/profile/')] | //a[contains(@href, 'candidate')] | //td//a[not(contains(@href, 'mailto'))]")
                        if len(new_candidates) > 0:
                            framework_success = True
                            print("  ✅ Syncfusion events επιτυχής!")

                except Exception as e:
                    print(f"  ❌ Syncfusion events απέτυχε: {e}")

            if framework_success:
                page += 1
                print(f"🎉 Επιτυχής μετάβαση στη σελίδα {page} με JavaScript framework!")
                continue

            # === ΜΕΘΟΔΟΣ 2: ΚΛΙΚ ΣΕ ΚΟΥΜΠΙΑ (FALLBACK) ===
            print(f"⚠️  Άμεση πλοήγηση απέτυχε, δοκιμάζει κλικ σε κουμπιά...")
            next_btn = None
            
            # 🔍 Αναζήτηση κουμπιών pagination
            print(f"🔘 Αναζητά κουμπί για σελίδα {next_page_num}...")

            # Μέθοδος 1: Αναζήτηση για αριθμό επόμενης σελίδας
            possible_selectors = [
                f"//a[text()='{next_page_num}' and not(contains(@class,'disabled')) and not(contains(@class,'active'))]",
                f"//a[contains(text(),'{next_page_num}') and not(contains(@class,'disabled')) and not(contains(@class,'active'))]",
                f"//button[text()='{next_page_num}' and not(contains(@class,'disabled')) and not(contains(@class,'active'))]"
            ]

            for i, selector in enumerate(possible_selectors, 1):
                try:
                    next_btn = driver.find_element(By.XPATH, selector)
                    print(f"  ✅ Selector {i}: Βρέθηκε κουμπί για σελίδα {next_page_num}")
                    break
                except:
                    print(f"  ❌ Selector {i}: Δεν βρέθηκε")
                    continue

            # Μέθοδος 2: Αναζήτηση για κουμπί ">" (επόμενη σελίδα)
            if not next_btn:
                print(f"🔘 Αναζητά κουμπί '>' για επόμενη σελίδα...")
                arrow_selectors = [
                    "//a[text()='>' and not(contains(@class,'disabled'))]",
                    "//a[contains(@class,'next') and not(contains(@class,'disabled'))]",
                    "//button[text()='>' and not(contains(@class,'disabled'))]"
                ]

                for i, selector in enumerate(arrow_selectors, 1):
                    try:
                        next_btn = driver.find_element(By.XPATH, selector)
                        print(f"  ✅ Arrow Selector {i}: Βρέθηκε κουμπί '>'")
                        break
                    except:
                        print(f"  ❌ Arrow Selector {i}: Δεν βρέθηκε")
                        continue
            
            if next_btn:
                print(f"🖱️  Προσπαθεί κλικ για σελίδα {page + 1}...")
                print(f"📋 Κουμπί - Text: '{next_btn.text}', Href: '{next_btn.get_attribute('href')}', Class: '{next_btn.get_attribute('class')}'")

                # Κάνει scroll στο κουμπί πριν το κλικ
                driver.execute_script("arguments[0].scrollIntoView(true);", next_btn)
                time.sleep(2)

                # Προσπαθεί διάφορους τρόπους κλικ
                click_success = False

                # Μέθοδος 1: JavaScript click
                print("  🔧 Μέθοδος 1: JavaScript click...")
                try:
                    driver.execute_script("arguments[0].click();", next_btn)
                    time.sleep(3)
                    if driver.current_url != current_url:
                        click_success = True
                        print("  ✅ JavaScript click επιτυχής!")
                except Exception as e:
                    print(f"  ❌ JavaScript click απέτυχε: {e}")

                # Μέθοδος 2: ActionChains click
                if not click_success:
                    print("  🔧 Μέθοδος 2: ActionChains click...")
                    try:
                        actions = ActionChains(driver)
                        actions.move_to_element(next_btn).click().perform()
                        time.sleep(3)
                        if driver.current_url != current_url:
                            click_success = True
                            print("  ✅ ActionChains click επιτυχής!")
                    except Exception as e:
                        print(f"  ❌ ActionChains click απέτυχε: {e}")

                # Μέθοδος 3: Selenium click
                if not click_success:
                    print("  🔧 Μέθοδος 3: Selenium click...")
                    try:
                        next_btn.click()
                        time.sleep(3)
                        if driver.current_url != current_url:
                            click_success = True
                            print("  ✅ Selenium click επιτυχής!")
                    except Exception as e:
                        print(f"  ❌ Selenium click απέτυχε: {e}")

                # Μέθοδος 4: JavaScript MouseEvent
                if not click_success:
                    print("  🔧 Μέθοδος 4: JavaScript MouseEvent...")
                    try:
                        driver.execute_script("""
                            var element = arguments[0];
                            var event = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            element.dispatchEvent(event);
                        """, next_btn)
                        time.sleep(3)
                        if driver.current_url != current_url:
                            click_success = True
                            print("  ✅ JavaScript MouseEvent επιτυχής!")
                    except Exception as e:
                        print(f"  ❌ JavaScript MouseEvent απέτυχε: {e}")

                # Μέθοδος 5: Άμεση πλοήγηση στο href
                if not click_success:
                    print("  🔧 Μέθοδος 5: Άμεση πλοήγηση στο href...")
                    try:
                        href = next_btn.get_attribute('href')
                        if href and href != current_url:
                            print(f"    🔗 Πλοηγείται στο: {href}")
                            driver.get(href)
                            time.sleep(3)
                            if driver.current_url != current_url:
                                click_success = True
                                print("  ✅ Άμεση πλοήγηση επιτυχής!")
                        else:
                            print("    ⚠️  Δεν υπάρχει έγκυρο href")
                    except Exception as e:
                        print(f"  ❌ Άμεση πλοήγηση απέτυχε: {e}")

                # Αποτελέσματα κλικ
                if click_success:
                    new_url = driver.current_url
                    print(f"🎉 Επιτυχές κλικ! Νέο URL: {new_url}")
                    page += 1
                    time.sleep(3)
                    print(f"✅ Φόρτωσε σελίδα {page}")
                else:
                    print("❌ Όλες οι μέθοδοι κλικ απέτυχαν.")
                    break
            else:
                print("❌ Δεν βρέθηκε κουμπί επόμενης σελίδας.")
                break
        else:
            print(f"🏁 Έφτασε στο όριο δοκιμής ({max_pages_to_test} σελίδες)")
            break

    print(f"\n{'='*50}")
    print(f"🏆 ΟΛΟΚΛΗΡΩΣΗ ΔΟΚΙΜΗΣ PAGINATION")
    print(f"{'='*50}")
    print(f"📊 Συνολικές σελίδες που δοκιμάστηκαν: {page}")
    print(f"🎯 Στόχος ήταν: {max_pages_to_test} σελίδες")

    if page > 1:
        print(f"✅ ΕΠΙΤΥΧΙΑ! Το pagination λειτουργεί σωστά!")
        print(f"🔄 Μπόρεσε να πλοηγηθεί σε {page} σελίδες")
        return True
    else:
        print(f"❌ ΑΠΟΤΥΧΙΑ! Το pagination δεν λειτουργεί.")
        print(f"⚠️  Παρέμεινε στη σελίδα 1")
        return False

def main():
    try:
        login()
        goto_my_ads()
        open_applications_for_ad_by_index(row_index=1)
        
        # Δοκιμάζει το pagination
        pagination_works = test_pagination()
        
        if pagination_works:
            print(f"\n🎉 ΕΠΙΤΥΧΙΑ! Το script μπορεί να αλλάξει σελίδες!")
            print(f"✅ Μπορείτε να τρέξετε το πλήρες κατέβασμα βιογραφικών.")
            print(f"🚀 Το script θα κατεβάσει όλα τα βιογραφικά από όλες τις σελίδες.")
        else:
            print(f"\n❌ ΑΠΟΤΥΧΙΑ! Το pagination δεν λειτουργεί.")
            print(f"⚠️  Χρειάζεται διόρθωση πριν το πλήρες κατέβασμα.")
            print(f"🔧 Το script θα κατεβάσει μόνο τα βιογραφικά από την πρώτη σελίδα.")
            
    except Exception as e:
        print(f"Σφάλμα: {e}")
    finally:
        input("\nΠατήστε Enter για να κλείσει το browser...")
        driver.quit()

if __name__ == "__main__":
    main()
