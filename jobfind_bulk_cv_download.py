import os, time, re, shutil
from pathlib import Path
from dotenv import load_dotenv

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# ---------- ΡΥΘΜΙΣΕΙΣ ----------
load_dotenv()
JOBFIND_EMAIL = os.getenv("JOBFIND_EMAIL") or "<EMAIL>"
JOBFIND_PASS  = os.getenv("JOBFIND_PASS")  or "password123"
BASE_URL      = os.getenv("BASE_URL") or "https://www.jobfind.gr"
DOWNLOAD_DIR  = Path(os.getenv("DOWNLOAD_DIR") or (Path.home() / "Downloads" / "JobfindCVs"))
HEADLESS      = False  # άλλαξε σε True για headless

DOWNLOAD_DIR.mkdir(parents=True, exist_ok=True)

# ---------- CHROME ----------
chrome_opts = webdriver.ChromeOptions()
prefs = {
    "download.default_directory": str(DOWNLOAD_DIR.resolve()),
    "download.prompt_for_download": False,
    "download.directory_upgrade": True,
    "plugins.always_open_pdf_externally": True,  # να γίνεται άμεσο download, όχι preview
}
chrome_opts.add_experimental_option("prefs", prefs)
if HEADLESS:
    chrome_opts.add_argument("--headless=new")
    chrome_opts.add_argument("--window-size=1920,1080")

driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()),
                          options=chrome_opts)
wait = WebDriverWait(driver, 20)

def safe_name(s: str) -> str:
    s = re.sub(r"[^\w\-\.\(\)\s]", "_", s, flags=re.UNICODE)
    return re.sub(r"\s+", " ", s).strip()

def login():
    print("Ανοίγει η σελίδα login...")
    driver.get("https://business.jobfind.gr/signin")

    # Περιμένει να φορτώσει η σελίδα και να εμφανιστούν τα πεδία
    print("Περιμένει για τα πεδία login...")

    # Προσπαθεί να βρει το email field με διάφορους τρόπους
    try:
        email_field = wait.until(EC.presence_of_element_located((By.XPATH, "//input[contains(@placeholder, 'Email') or @type='email' or contains(@name, 'email') or contains(@id, 'email')]")))
    except:
        # Εναλλακτικά, βρες το πρώτο input field
        email_field = wait.until(EC.presence_of_element_located((By.XPATH, "//input[@type='text' or @type='email']")))

    email_field.send_keys(JOBFIND_EMAIL)
    print(f"Εισήχθη email: {JOBFIND_EMAIL}")

    # Προσπαθεί να βρει το password field
    try:
        password_field = driver.find_element(By.XPATH, "//input[@type='password']")
    except:
        password_field = driver.find_element(By.XPATH, "//input[contains(@placeholder, 'password') or contains(@placeholder, 'κωδικός') or contains(@name, 'password')]")

    password_field.send_keys(JOBFIND_PASS)
    print("Εισήχθη κωδικός πρόσβασης")

    # Βρίσκει και πατάει το κουμπί login
    try:
        login_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Είσοδος')] | //input[@type='submit' and contains(@value, 'Είσοδος')]")
        login_button.click()
    except:
        # Εναλλακτικά, πάτα Enter στο password field
        password_field.send_keys(Keys.RETURN)

    print("Πατήθηκε το κουμπί login...")

    # Περιμένει να ολοκληρωθεί το login
    time.sleep(3)
    print("Login ολοκληρώθηκε.")

def goto_my_ads():
    print("Αναζητά το link για τις αγγελίες...")

    # Προσπαθεί να βρει το κουμπί "Δείτε τις αγγελίες σας"
    try:
        ads_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Δείτε τις αγγελίες σας')] | //button[contains(text(), 'Δείτε τις αγγελίες σας')]")))
        driver.execute_script("arguments[0].click();", ads_button)
        print("Πατήθηκε το κουμπί 'Δείτε τις αγγελίες σας'")
        return
    except Exception as e:
        print(f"Δεν βρέθηκε το κουμπί 'Δείτε τις αγγελίες σας': {e}")

    # Εναλλακτικά, προσπαθεί να βρει άλλα links
    try:
        # Προσπάθεια για "Οι Αγγελίες μου"
        ads_link = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Οι Αγγελίες μου')] | //a[contains(text(), 'αγγελίες')] | //a[contains(text(), 'Αγγελίες')]")))
        driver.execute_script("arguments[0].click();", ads_link)
        print("Πατήθηκε το link για τις αγγελίες")
        return
    except Exception as e:
        print(f"Δεν βρέθηκε link για αγγελίες: {e}")

    # Τελευταία προσπάθεια - αναζήτηση για οποιοδήποτε link που περιέχει "αγγελ"
    try:
        any_ads_link = wait.until(EC.element_to_be_clickable((By.XPATH, "//a[contains(translate(text(), 'ΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ', 'αβγδεζηθικλμνξοπρστυφχψω'), 'αγγελ')]")))
        driver.execute_script("arguments[0].click();", any_ads_link)
        print("Πατήθηκε γενικό link για αγγελίες")
    except Exception as e:
        print(f"Δεν βρέθηκε κανένα link για αγγελίες: {e}")
        raise RuntimeError("Δεν μπόρεσε να βρει το link για τις αγγελίες")

def open_applications_for_ad_by_index(row_index=1):
    """
    Βρίσκει αγγελία με κατάσταση 'Δημοσιευμένη' και πατάει στη στήλη 'Βιογραφικά'.
    row_index: 1-based index της αγγελίας (π.χ. 1 = πρώτη γραμμή).
    """
    print("Αναζητά αγγελίες με κατάσταση 'Δημοσιευμένη'...")

    # Περιμένει να φορτώσει η σελίδα με τις αγγελίες
    time.sleep(3)

    try:
        # Βρίσκει όλες τις γραμμές του πίνακα
        table_rows = driver.find_elements(By.XPATH, "//table//tr[td]")
        print(f"Βρέθηκαν {len(table_rows)} γραμμές στον πίνακα")

        published_ads = []

        for i, row in enumerate(table_rows):
            try:
                # Ελέγχει αν η γραμμή περιέχει κατάσταση "Δημοσιευμένη"
                status_cells = row.find_elements(By.XPATH, ".//td[contains(text(), 'Δημοσιευμένη') or contains(., 'Δημοσιευμένη')]")

                if status_cells:
                    print(f"Βρέθηκε δημοσιευμένη αγγελία στη γραμμή {i+1}")

                    # Βρίσκει όλα τα κελιά της γραμμής
                    all_cells = row.find_elements(By.XPATH, ".//td")

                    # Αναζητά links στη στήλη βιογραφικά που περιέχουν αριθμούς
                    cv_links = row.find_elements(By.XPATH, ".//td//a[contains(@href, 'jobads/apps/') or contains(@href, 'applications')]")

                    for link in cv_links:
                        text = link.text.strip()
                        href = link.get_attribute("href") or ""
                        print(f"  Βρέθηκε CV link: '{text}' -> {href}")

                        if text.isdigit() and int(text) > 0:
                            published_ads.append((i+1, text, link))
                            print(f"Βρέθηκε δημοσιευμένη αγγελία στη γραμμή {i+1} με {text} βιογραφικά")
                            break

                    # Εναλλακτικά, αναζητά οποιοδήποτε link με αριθμό στη γραμμή
                    if not cv_links:
                        all_links = row.find_elements(By.XPATH, ".//td//a[normalize-space(text())]")
                        for link in all_links:
                            text = link.text.strip()
                            href = link.get_attribute("href") or ""
                            print(f"  Εναλλακτικό link: '{text}' -> {href}")
                            if text.isdigit() and int(text) > 0:
                                published_ads.append((i+1, text, link))
                                print(f"Βρέθηκε δημοσιευμένη αγγελία στη γραμμή {i+1} με {text} βιογραφικά (εναλλακτικό)")
                                break

            except Exception as e:
                print(f"Σφάλμα στη γραμμή {i+1}: {e}")
                continue

        if not published_ads:
            print("Δεν βρέθηκαν δημοσιευμένες αγγελίες. Προσπαθεί με όλα τα βιογραφικά...")

            # Debug: εκτυπώνει όλα τα κελιά του πίνακα για να δούμε τι υπάρχει
            all_cells = driver.find_elements(By.XPATH, "//table//td")
            print("Περιεχόμενα κελιών πίνακα:")
            for i, cell in enumerate(all_cells[:50]):  # Μόνο τα πρώτα 50 για να μην γεμίσει η οθόνη
                text = cell.text.strip()
                if text:
                    print(f"  Κελί {i+1}: '{text}'")

            # Fallback: βρίσκει όλα τα links με αριθμούς
            all_cv_links = driver.find_elements(By.XPATH, "//table//a[normalize-space(text())]")
            print(f"Βρέθηκαν {len(all_cv_links)} links στον πίνακα")

            for i, link in enumerate(all_cv_links):
                text = link.text.strip()
                href = link.get_attribute("href") or ""
                print(f"  Link {i+1}: '{text}' -> {href}")
                if text.isdigit() and int(text) > 0:
                    published_ads.append((1, text, link))
                    print(f"Βρέθηκε link με {text} βιογραφικά")
                    break

            # Εναλλακτική: αναζήτηση για οποιοδήποτε link που μπορεί να είναι βιογραφικά
            if not published_ads:
                print("Αναζητά εναλλακτικά links...")
                alt_links = driver.find_elements(By.XPATH, "//a[contains(@href, 'application') or contains(@href, 'cv') or contains(@href, 'candidate') or contains(text(), '47') or contains(text(), '1185')]")
                for link in alt_links:
                    text = link.text.strip()
                    print(f"Εναλλακτικό link: '{text}'")
                    if text:
                        published_ads.append((1, text, link))
                        break

        if not published_ads:
            raise RuntimeError("Δεν βρέθηκαν αγγελίες με βιογραφικά.")

        # Επιλέγει την πρώτη δημοσιευμένη αγγελία ή τη συγκεκριμένη που ζητήθηκε
        if row_index <= len(published_ads):
            selected_ad = published_ads[row_index - 1]
        else:
            selected_ad = published_ads[0]
            print(f"Ζητήθηκε γραμμή {row_index}, αλλά επιλέχθηκε η πρώτη διαθέσιμη")

        row_num, cv_count, cv_link = selected_ad
        print(f"Πατάει στη στήλη βιογραφικά με {cv_count} βιογραφικά...")

        # Κάνει scroll στο element αν χρειάζεται
        driver.execute_script("arguments[0].scrollIntoView(true);", cv_link)
        time.sleep(1)

        # Πατάει το link
        driver.execute_script("arguments[0].click();", cv_link)

        # Περιμένει να φορτώσει η λίστα υποψηφίων
        time.sleep(5)
        print("Φόρτωσε η λίστα υποψηφίων.")

    except Exception as e:
        print(f"Σφάλμα κατά την αναζήτηση αγγελιών: {e}")
        raise

def iterate_candidates_and_download_all(max_pages=50, max_downloads=1000):
    """
    Στρατηγική: Πηγαίνει στην τελευταία σελίδα και κατεβάζει αναποδα για να αποφύγει pagination issues.
    Κατεβάζει τα βιογραφικά από το παλαιότερο στο νεότερο:
    - Επεξεργάζεται σελίδες από τελευταία στην πρώτη
    - Μέσα σε κάθε σελίδα, αντιστρέφει τη σειρά των υποψηφίων

    Args:
        max_pages: Μέγιστος αριθμός σελίδων για επεξεργασία
        max_downloads: Μέγιστος αριθμός βιογραφικών για κατέβασμα
    """
    # Αποθηκεύει το URL της λίστας υποψηφίων για να επιστρέφει εδώ
    candidates_list_url = driver.current_url
    print(f"Αποθηκεύτηκε URL λίστας υποψηφίων: {candidates_list_url}")

    total_processed = 0

    # ΒΗΜΑ 1: Βρίσκει την τελευταία σελίδα
    print(f"\n{'='*80}")
    print(f"🔍 ΑΝΑΖΗΤΗΣΗ ΤΕΛΕΥΤΑΙΑΣ ΣΕΛΙΔΑΣ")
    print(f"{'='*80}")

    last_page = find_last_page()
    if last_page is None:
        print("❌ Δεν μπόρεσε να βρει την τελευταία σελίδα. Ξεκινά από τη σελίδα 1.")
        last_page = 1
    else:
        print(f"🎯 Τελευταία σελίδα: {last_page}")

    # ΒΗΜΑ 2: Πηγαίνει στην τελευταία σελίδα
    if last_page > 1:
        print(f"\n🚀 ΜΕΤΑΒΑΣΗ ΣΤΗΝ ΤΕΛΕΥΤΑΙΑ ΣΕΛΙΔΑ ({last_page})")
        success = go_to_page(last_page)
        if not success:
            print(f"❌ Δεν μπόρεσε να πάει στη σελίδα {last_page}. Ξεκινά από τη σελίδα 1.")
            last_page = 1

    # ΒΗΜΑ 3: Κατεβάζει αναποδα από την τελευταία στην πρώτη
    print(f"\n{'='*80}")
    print(f"📥 ΚΑΤΕΒΑΣΜΑ ΒΙΟΓΡΑΦΙΚΩΝ (ΑΝΑΠΟΔΑ)")
    print(f"{'='*80}")
    print(f"🔄 Θα επεξεργαστεί σελίδες από {last_page} έως 1")

    for current_page in range(last_page, 0, -1):  # Αναποδα: από τελευταία στην πρώτη
        if total_processed >= max_downloads:
            print(f"\n🛑 Έφτασε το όριο των {max_downloads} κατεβασμάτων")
            break

        print(f"\n{'='*80}")
        print(f"📄 ΣΕΛΙΔΑ {current_page} (από {last_page})")
        print(f"{'='*80}")

        # Πηγαίνει στη συγκεκριμένη σελίδα (αν δεν είναι ήδη εκεί)
        if current_page != last_page:  # Για την πρώτη επανάληψη είμαστε ήδη στην τελευταία
            success = go_to_page(current_page)
            if not success:
                print(f"❌ Δεν μπόρεσε να πάει στη σελίδα {current_page}. Παραλείπει.")
                continue

        # Επεξεργάζεται τους υποψηφίους στη σελίδα
        processed_in_page = process_candidates_in_current_page(current_page, total_processed, max_downloads)
        total_processed += processed_in_page

        print(f"\n✅ Σελίδα {current_page} ολοκληρώθηκε: {processed_in_page} βιογραφικά")
        print(f"📊 Συνολικά κατεβασμένα: {total_processed}")

    # Τελικά στατιστικά
    print(f"\n{'='*80}")
    print(f"🏁 ΟΛΟΚΛΗΡΩΣΗ ΚΑΤΕΒΑΣΜΑΤΟΣ ΒΙΟΓΡΑΦΙΚΩΝ")
    print(f"{'='*80}")
    print(f"📊 Συνολικές σελίδες που επεξεργάστηκαν: {last_page}")
    print(f"📊 Συνολικά βιογραφικά που κατεβάστηκαν: {total_processed}")
    print(f"📁 Αρχεία PDF στον φάκελο: {len(list(DOWNLOAD_DIR.glob('*.pdf')))}")
    print(f"📂 Φάκελος κατεβάσματος: {DOWNLOAD_DIR}")
    print(f"⏱️  Χρόνος ολοκλήρωσης: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*80}")

    return total_processed


def find_last_page():
    """
    Βρίσκει την τελευταία σελίδα από το pagination
    """
    try:
        # Μέθοδος 1: Αναζητά το τελευταίο αριθμό σελίδας
        page_buttons = driver.find_elements(By.XPATH, "//a[contains(@class,'e-numericitem')]")
        if page_buttons:
            page_numbers = []
            for btn in page_buttons:
                text = btn.text.strip()
                if text.isdigit():
                    page_numbers.append(int(text))

            if page_numbers:
                last_page = max(page_numbers)
                print(f"📊 Βρέθηκαν σελίδες: {sorted(page_numbers)}")
                return last_page

        # Μέθοδος 2: Αναζητά πληροφορίες pagination
        try:
            pagination_info = driver.find_element(By.XPATH, "//span[contains(text(), 'από') and contains(text(), 'σελίδες')]")
            pagination_text = pagination_info.text
            print(f"📊 Pagination info: {pagination_text}")

            # Εξάγει τον αριθμό σελίδων (π.χ. "1 από 5 σελίδες")
            import re
            match = re.search(r'από\s+(\d+)\s+σελίδες', pagination_text)
            if match:
                return int(match.group(1))
        except:
            pass

        # Μέθοδος 3: Syncfusion API
        try:
            result = driver.execute_script("""
                var pagerElements = document.querySelectorAll('.e-pager');
                if (pagerElements.length > 0) {
                    var pager = pagerElements[0];
                    if (pager.ej2_instances && pager.ej2_instances.length > 0) {
                        var pagerInstance = pager.ej2_instances[0];
                        return pagerInstance.totalPages || null;
                    }
                }
                return null;
            """)

            if result and isinstance(result, (int, float)):
                print(f"📊 Syncfusion API: {result} σελίδες")
                return int(result)
        except:
            pass

        print("⚠️  Δεν μπόρεσε να βρει την τελευταία σελίδα")
        return None

    except Exception as e:
        print(f"❌ Σφάλμα κατά την αναζήτηση τελευταίας σελίδας: {e}")
        return None


def go_to_page(page_number):
    """
    Πηγαίνει σε συγκεκριμένη σελίδα
    """
    print(f"🔄 Μετάβαση στη σελίδα {page_number}...")

    try:
        # Μέθοδος 1: Syncfusion API
        result = driver.execute_script(f"""
            var pagerElements = document.querySelectorAll('.e-pager');
            if (pagerElements.length > 0) {{
                var pager = pagerElements[0];
                if (pager.ej2_instances && pager.ej2_instances.length > 0) {{
                    var pagerInstance = pager.ej2_instances[0];
                    if (pagerInstance.goToPage) {{
                        pagerInstance.goToPage({page_number});
                        return 'success';
                    }}
                }}
            }}
            return 'failed';
        """)

        if result == 'success':
            time.sleep(2)
            print(f"✅ Syncfusion API: Μετάβαση στη σελίδα {page_number}")
            return True
    except Exception as e:
        print(f"❌ Syncfusion API απέτυχε: {e}")

    try:
        # Μέθοδος 2: Κλικ στο κουμπί σελίδας
        page_button = driver.find_element(By.XPATH, f"//a[contains(@class,'e-numericitem') and text()='{page_number}']")
        driver.execute_script("arguments[0].scrollIntoView(true);", page_button)

        from selenium.webdriver.common.action_chains import ActionChains
        actions = ActionChains(driver)
        actions.move_to_element(page_button).click().perform()

        time.sleep(2)
        print(f"✅ Κλικ: Μετάβαση στη σελίδα {page_number}")
        return True

    except Exception as e:
        print(f"❌ Κλικ απέτυχε: {e}")

    return False


def process_candidates_in_current_page(page_number, total_processed_so_far, max_downloads):
    """
    Επεξεργάζεται όλους τους υποψηφίους στην τρέχουσα σελίδα
    """
    candidates_list_url = driver.current_url
    processed_count = 0

    print(f"🔍 Αναζήτηση υποψηφίων στη σελίδα {page_number}...")

    # Περιμένει να φορτώσει η σελίδα υποψηφίων
    time.sleep(2)

    # Προσπαθεί να βρει τον πίνακα υποψηφίων
    try:
        wait.until(EC.presence_of_element_located((By.XPATH, "//table | //div[contains(@class, 'candidate')] | //div[contains(@class, 'applicant')]")))
        print("✅ Βρέθηκε πίνακας/λίστα υποψηφίων")
    except:
        print("⚠️  Δεν βρέθηκε πίνακας υποψηφίων, συνεχίζει...")

        # Μάζεψε όλα τα links ονομάτων (στήλη Ονοματεπώνυμο)
        name_links = []

        # Προσπάθεια 1: Αναζήτηση για links σε πίνακα
        try:
            table_links = driver.find_elements(By.XPATH, "//table//a[normalize-space(text()) and not(contains(@href, 'mailto:'))]")
            name_links.extend(table_links)
            print(f"Βρέθηκαν {len(table_links)} links σε πίνακα")
        except:
            pass

        # Προσπάθεια 2: Αναζήτηση για links που περιέχουν ονόματα
        if not name_links:
            try:
                all_links = driver.find_elements(By.XPATH, "//a[normalize-space(text()) and string-length(normalize-space(text())) > 3]")
                for link in all_links:
                    href = link.get_attribute("href") or ""
                    text = link.text.strip()
                    # Φιλτράρει links που μοιάζουν με ονόματα υποψηφίων
                    if (text and len(text.split()) >= 2 and
                        not any(x in href.lower() for x in ['mailto:', 'javascript:', '#']) and
                        not any(x in text.lower() for x in ['back', 'next', 'previous', 'home', 'logout'])):
                        name_links.append(link)
                print(f"Βρέθηκαν {len(name_links)} πιθανά links ονομάτων")
            except:
                pass

        candidates = []
        for a in name_links:
            text = a.text.strip()
            href = a.get_attribute("href") or ""
            # Χονδρικό φίλτρο να μην πάρουμε άσχετα actions
            if text and (("/Employer/" in href or "/Candidate/" in href or
                         "candidate" in href.lower() or "applicant" in href.lower() or
                         len(text.split()) >= 2)):  # Ονόματα έχουν συνήθως 2+ λέξεις
                candidates.append((text, a))
                print(f"  Υποψήφιος: {text}")

        print(f"👥 Βρέθηκαν {len(candidates)} υποψήφιοι στη σελίδα {page_number}")
        remaining_downloads = max_downloads - total_processed_so_far
        candidates_to_process = min(len(candidates), remaining_downloads)
        print(f"📋 Θα επεξεργαστούν {candidates_to_process} βιογραφικά")

        # Αντιστρέφει τη σειρά των υποψηφίων για να κατεβάζει από παλαιότερο στο νεότερο
        # (αφού οι σελίδες επεξεργάζονται από τελευταία στην πρώτη)
        candidates_reversed = list(reversed(candidates[:candidates_to_process]))
        print(f"🔄 Αντιστροφή σειράς υποψηφίων για χρονολογική σειρά (παλαιότερο → νεότερο)")

        # Επεξεργάζεται τους υποψηφίους έναν προς έναν σε αντίστροφη σειρά
        for candidate_index in range(len(candidates_reversed)):
            try:
                print(f"\n{'─'*60}")
                print(f"👤 ΥΠΟΨΗΦΙΟΣ {candidate_index + 1}/{len(candidates_reversed)} (Σελίδα {page_number})")
                print(f"📊 Συνολική πρόοδος: {total_processed_so_far + processed_count + 1}")
                print(f"{'─'*60}")

                # Χρησιμοποιεί τους αντεστραμμένους υποψηφίους
                if candidate_index >= len(candidates_reversed):
                    print(f"❌ Δεν υπάρχει υποψήφιος στη θέση {candidate_index + 1}")
                    continue

                full_name, anchor = candidates_reversed[candidate_index]
                print(f"👤 Επεξεργάζεται: {full_name}")
                print(f"🔗 Link: {anchor.get_attribute('href')}")

                # Καταγράφει τα υπάρχοντα αρχεία πριν το κατέβασμα
                existing_files_before = set(f.name for f in DOWNLOAD_DIR.glob("*") if f.is_file())
                print(f"📁 Υπάρχοντα αρχεία: {len(existing_files_before)}")

                # Κάνει scroll στο element πριν το κλικ
                print(f"🖱️  Κλικ στο προφίλ του υποψηφίου...")
                driver.execute_script("arguments[0].scrollIntoView(true);", anchor)
                driver.execute_script("arguments[0].click();", anchor)

                # Περιμένει να φορτώσει η σελίδα του υποψηφίου
                print(f"⏳ Φόρτωση σελίδας υποψηφίου...")
                time.sleep(2)  # Μειωμένη καθυστέρηση

                # Αναζητά tab "Βιογραφικό"
                print(f"🔍 Αναζήτηση tab 'Βιογραφικό'...")
                try:
                    cv_tab = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Βιογραφικό")))
                    print(f"✅ Βρέθηκε tab 'Βιογραφικό' (μέθοδος 1)")
                except Exception:
                    try:
                        cv_tab = wait.until(EC.element_to_be_clickable(
                            (By.XPATH, "//a[contains(.,'Βιογραφικό')]")))
                        print(f"✅ Βρέθηκε tab 'Βιογραφικό' (μέθοδος 2)")
                    except Exception:
                        print(f"❌ {full_name}: Δεν βρέθηκε tab Βιογραφικό")
                        driver.back()
                        time.sleep(1)
                        continue

                print(f"🖱️  Κλικ στο tab 'Βιογραφικό'...")
                driver.execute_script("arguments[0].click();", cv_tab)
                print(f"⏳ Φόρτωση tab βιογραφικό...")
                time.sleep(2)  # Μειωμένη καθυστέρηση

                # Αναζητά κουμπί "Κατέβασμα βιογραφικού"
                print(f"🔍 Αναζήτηση κουμπιού κατεβάσματος...")
                try:
                    dl_btn = wait.until(EC.element_to_be_clickable(
                        (By.XPATH, "//a[contains(.,'Κατέβασμα') and contains(.,'βιογραφικού')] | //button[contains(.,'Κατέβασμα')]")
                    ))
                    print(f"✅ Βρέθηκε κουμπί κατεβάσματος (μέθοδος 1)")
                except Exception:
                    try:
                        dl_btn = wait.until(EC.element_to_be_clickable(
                            (By.XPATH, "//a[contains(.,'Λήψη') and contains(.,'βιογραφικού')] | //a[contains(.,'Download')]")
                        ))
                        print(f"✅ Βρέθηκε κουμπί κατεβάσματος (μέθοδος 2)")
                    except Exception:
                        print(f"❌ {full_name}: Δεν βρέθηκε κουμπί κατεβάσματος")
                        driver.back()
                        time.sleep(1)
                        continue

                # Κάνει scroll στο κουμπί κατεβάσματος
                print(f"🖱️  Κλικ στο κουμπί κατεβάσματος...")
                driver.execute_script("arguments[0].scrollIntoView(true);", dl_btn)
                driver.execute_script("arguments[0].click();", dl_btn)

                # Περιμένει να ξεκινήσει το κατέβασμα
                print(f"⏳ Αναμονή έναρξης κατεβάσματος...")
                time.sleep(1)  # Μειωμένη καθυστέρηση

                # Περιμένει να κατέβει το αρχείο με μειωμένο timeout
                print(f"📥 Αναμονή ολοκλήρωσης κατεβάσματος (timeout: 30s)...")
                files_before_download = wait_for_download(timeout=30, existing_files_before=existing_files_before)

                # Μετονομάζει το αρχείο χρησιμοποιώντας τη λίστα των παλιών αρχείων
                print(f"📝 Μετονομασία αρχείου...")
                renamed_file = rename_last_download(full_name, existing_files_before=files_before_download)
                if renamed_file:
                    print(f"✅ Αρχείο αποθηκεύτηκε ως: {renamed_file}")
                else:
                    print(f"⚠️  Προβλήμα με τη μετονομασία του αρχείου για {full_name}")

                # Επιστρέφει στη λίστα υποψηφίων χρησιμοποιώντας το αποθηκευμένο URL
                print(f"🔄 Επιστροφή στη λίστα υποψηφίων...")
                driver.get(candidates_list_url)
                time.sleep(2)  # Μειωμένη καθυστέρηση

                processed_count += 1
                total_processed += 1
                print(f"🎉 ✓ {full_name}")
                print(f"📊 Πρόοδος σελίδας: {processed_count}/{len(candidates_reversed)}")
                print(f"📊 Συνολική πρόοδος: {total_processed}")
                print(f"{'─'*60}")

            except Exception as e:
                print(f"   ✕ {full_name if 'full_name' in locals() else f'Υποψήφιος {candidate_index + 1}'}: {e}")
                # Προσπαθεί να επιστρέψει στη λίστα χρησιμοποιώντας το αποθηκευμένο URL
                try:
                    print(f"Επιστροφή στη λίστα υποψηφίων μετά από σφάλμα: {candidates_list_url}")
                    driver.get(candidates_list_url)
                    time.sleep(1)  # Μειωμένη καθυστέρηση
                except Exception as return_error:
                    print(f"Σφάλμα κατά την επιστροφή στη λίστα: {return_error}")
                    pass

    return processed_count


# Παλιός κώδικας που δεν χρειάζεται πια - θα αφαιρεθεί
def old_pagination_logic():
    """
    Παλιός κώδικας pagination που αντικαταστάθηκε με τη νέα στρατηγική
    """
    pass




def wait_for_download(timeout=30, existing_files_before=None):  # Μειωμένο default timeout
    """
    Περιμένει να ολοκληρωθεί κάποιο download (.crdownload -> complete).
    Βελτιωμένη έκδοση που ελέγχει για νέα αρχεία και περιμένει περισσότερο.
    Επιστρέφει τη λίστα των αρχείων που υπήρχαν πριν το κατέβασμα.
    """
    print("Περιμένει να ολοκληρωθεί το κατέβασμα...")

    # Καταγράφει τα υπάρχοντα αρχεία πριν το κατέβασμα (αν δεν δόθηκαν)
    if existing_files_before is None:
        existing_files_before = set(f.name for f in DOWNLOAD_DIR.glob("*") if f.is_file())

    start_time = time.time()
    download_started = False

    while time.time() - start_time < timeout:
        # Ελέγχει αν υπάρχουν .crdownload αρχεία (κατέβασμα σε εξέλιξη)
        crdownload_files = list(DOWNLOAD_DIR.glob("*.crdownload"))

        if crdownload_files and not download_started:
            download_started = True
            print("Το κατέβασμα ξεκίνησε...")

        # Αν δεν υπάρχουν .crdownload αρχεία και το κατέβασμα είχε ξεκινήσει
        if not crdownload_files and download_started:
            time.sleep(1)  # Μειωμένη καθυστέρηση για σιγουριά
            print("Το κατέβασμα ολοκληρώθηκε.")
            return existing_files_before

        # Ελέγχει για νέα αρχεία ακόμα και αν δεν υπήρχε .crdownload
        current_files = set(f.name for f in DOWNLOAD_DIR.glob("*") if f.is_file())
        new_files = current_files - existing_files_before

        if new_files and not download_started:
            print(f"Βρέθηκε νέο αρχείο: {list(new_files)}")
            time.sleep(1)  # Μειωμένη καθυστέρηση για σιγουριά
            return existing_files_before

        time.sleep(0.5)  # Μειωμένη καθυστέρηση στο loop

    print(f"Timeout κατεβάσματος μετά από {timeout} δευτερόλεπτα")
    return existing_files_before

def rename_last_download(full_name: str, existing_files_before=None):
    """
    Βρίσκει το πιο πρόσφατο αρχείο που κατέβηκε και το μετονομάζει σε
    '<Ονοματεπώνυμο>.pdf' αν είναι PDF.
    Χρησιμοποιεί τη λίστα existing_files_before για να βρει μόνο νέα αρχεία.
    """
    current_files = set(f.name for f in DOWNLOAD_DIR.glob("*") if f.is_file())

    # Αν έχουμε τη λίστα των παλιών αρχείων, βρίσκει μόνο τα νέα
    if existing_files_before is not None:
        new_files = current_files - existing_files_before
        if not new_files:
            print(f"Δεν βρέθηκε νέο αρχείο για {full_name}")
            return

        # Βρίσκει το πιο πρόσφατο από τα νέα αρχεία
        new_file_paths = [DOWNLOAD_DIR / fname for fname in new_files]
        last = max(new_file_paths, key=lambda p: p.stat().st_mtime)
    else:
        # Fallback: παίρνει το πιο πρόσφατο αρχείο γενικά
        files = list(DOWNLOAD_DIR.glob("*"))
        if not files:
            print(f"Δεν βρέθηκε αρχείο για {full_name}")
            return
        last = max(files, key=lambda p: p.stat().st_mtime)

    # Ελέγχει αν είναι έγκυρο αρχείο βιογραφικού
    if last.suffix.lower() not in [".pdf", ".doc", ".docx"]:
        print(f"Το αρχείο {last.name} δεν είναι έγκυρο βιογραφικό για {full_name}")
        return

    # Δημιουργεί το νέο όνομα
    safe_full_name = safe_name(full_name)
    target = DOWNLOAD_DIR / f"{safe_full_name}{last.suffix.lower()}"

    # Αποφεύγει overwrite προσθέτοντας αριθμό
    i = 2
    final = target
    while final.exists():
        final = DOWNLOAD_DIR / f"{safe_full_name} ({i}){last.suffix.lower()}"
        i += 1

    try:
        print(f"Μετονομάζει '{last.name}' σε '{final.name}'")
        shutil.move(str(last), str(final))
        return final.name
    except Exception as e:
        print(f"Σφάλμα κατά τη μετονομασία: {e}")
        return None

def main():
    start_time = time.time()
    total_downloaded = 0

    try:
        print(f"\n{'='*80}")
        print(f"🚀 ΕΚΚΙΝΗΣΗ ΚΑΤΕΒΑΣΜΑΤΟΣ ΒΙΟΓΡΑΦΙΚΩΝ")
        print(f"{'='*80}")
        print(f"⏰ Ώρα έναρξης: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📂 Φάκελος κατεβάσματος: {DOWNLOAD_DIR}")
        print(f"{'='*80}")

        login()
        goto_my_ads()
        # άνοιξε τη στήλη "Βιογραφικά" για την πρώτη αγγελία (1 = πρώτη γραμμή)
        open_applications_for_ad_by_index(row_index=1)
        total_downloaded = iterate_candidates_and_download_all()

        # Τελικά στατιστικά
        end_time = time.time()
        duration = end_time - start_time
        duration_str = f"{int(duration//3600):02d}:{int((duration%3600)//60):02d}:{int(duration%60):02d}"

        print(f"\n{'='*80}")
        print(f"🎉 ΕΠΙΤΥΧΗΣ ΟΛΟΚΛΗΡΩΣΗ!")
        print(f"{'='*80}")
        print(f"📊 Συνολικά κατεβασμένα βιογραφικά: {total_downloaded}")
        print(f"⏱️  Συνολικός χρόνος: {duration_str}")
        print(f"📂 Φάκελος: {DOWNLOAD_DIR}")
        print(f"📁 Αρχεία PDF: {len(list(DOWNLOAD_DIR.glob('*.pdf')))}")
        print(f"{'='*80}")

    except Exception as e:
        print(f"\n❌ Σφάλμα κατά την εκτέλεση: {e}")
        raise
    finally:
        print(f"\n🔒 Κλείσιμο browser...")
        driver.quit()

if __name__ == "__main__":
    main()
